{% extends "be/include/preline-base.html" %}

{% block extrahead %}

    {% set menu = 'PRODUCT_COLLECTION' %}    
    {% set editText = "Modifica rivenditore" %}
    {% set newText = "Nuovo rivenditore" %}
    
    <title>{% if curDealer.id is not empty %}{{ editText }}{% else %}{{ newText }}{% endif %}</title>

    <!-- Page Libs -->
    {% include "be/include/snippets/plugins/datatable.html" %}
    {% include "be/include/snippets/plugins/daterangepicker.html" %}
    {% include "be/include/snippets/plugins/filepond.html" %}    
    {% include "be/include/snippets/plugins/validate.html" %}
    {% include "be/include/snippets/plugins/maxlength.html" %}

{% endblock %}

{% block content %}
<div class="lg:ps-65">
    <!-- Breadcrumb -->
    <ol class="lg:hidden pt-3 md:pt-5 sm:pb-2 md:pb-0 px-2 sm:px-5 flex items-center whitespace-nowrap">
        <li class="flex items-center text-sm text-gray-600 dark:text-neutral-500">
            Prodotti
            <svg class="shrink-0 overflow-visible size-4 ms-1.5 text-gray-400 dark:text-neutral-600" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path d="M6 13L10 3" stroke="currentColor" stroke-linecap="round"></path>
            </svg>
        </li>
        <li class="ps-1.5 flex items-center truncate font-semibold text-gray-800 dark:text-neutral-200 text-sm truncate">
            <span class="truncate">Prodotto</span>
        </li>
    </ol>
    <!-- End Breadcrumb -->

    <div class="p-2 sm:p-5 md:pt-5 space-y-5">

        <!-- Account Nav -->
        <div class="relative overflow-hidden flex justify-center md:justify-start" data-hs-scroll-nav='{
             "autoCentering": true
             }'>
            <nav class="hs-scroll-nav-body flex flex-nowrap overflow-x-auto [&::-webkit-scrollbar]:h-0 snap-x snap-mandatory pb-1.5">
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 bg-white border-gray-200! focus:text-gray-800 shadow-2xs dark:bg-neutral-800 dark:border-neutral-700! dark:focus:text-neutral-200 active" href="../../pro/dashboard/account-profile.html">
                    Informazioni
                </a>
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 " href="../../pro/dashboard/account-notifications.html">
                    Garanzie
                </a>
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 " href="../../pro/dashboard/account-integrations.html">
                    Tariffa
                </a>
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 " href="../../pro/dashboard/account-workspace.html">
                    Canali vendita
                </a>
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 " href="../../pro/dashboard/account-plan-and-billing.html">
                    Regole premi
                </a>
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 " href="../../pro/dashboard/account-members.html">
                    Regole campi
                </a>
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 " href="../../pro/dashboard/account-preferences.html">
                    Documenti e firme
                </a>                
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 " href="../../pro/dashboard/account-members.html">
                    Pubblicazione
                </a>
                <a class="snap-start inline-flex items-center gap-x-2 py-2 px-3 text-sm whitespace-nowrap border border-transparent text-gray-800 hover:text-gray-500 rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:text-gray-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:text-neutral-400 " href="../../pro/dashboard/account-members.html">
                    Test
                </a>
            </nav>
        </div>
        <!-- End Account Nav -->

        {% include "be/include/snippets/pojo/product-form-2-warranties.html" %}
        {#
        {% include "be/include/snippets/pojo/product-form-3-tariff.html" %}
        {% include "be/include/snippets/pojo/product-form-4-channels.html" %}
        {% include "be/include/snippets/pojo/product-form-1-info.html" %}
        
        {% include "be/include/snippets/pojo/product-form-5-pricing-rules.html" %}
        {% include "be/include/snippets/pojo/product-form-6-fields-rules.html" %}        
        {% include "be/include/snippets/pojo/product-form-7-documents-signature.html" %}
        {% include "be/include/snippets/pojo/product-form-8-publishing.html" %}
        {% include "be/include/snippets/pojo/product-form-9-test.html" %}
        
        #}
                      

    </div>
    
    
</div>
{% endblock %}
{% block pagescript %}

    <!-- Reload -->
    <script class="reload-script-on-load">
        addRoute('BE_PRODUCT_DATA', '{{ routes("BE_PRODUCT_DATA") }}');
        addRoute('BE_PRODUCT_OPERATE', '{{ routes("BE_PRODUCT_OPERATE") }}');
        addRoute('BE_PRODUCT_FORM', '{{ routes("BE_PRODUCT_FORM") }}');
        addRoute('BE_PRODUCT_SAVE', '{{ routes("BE_PRODUCT_SAVE") }}');
        addRoute('BE_PRODUCT_VIEW', '{{ routes("BE_PRODUCT_VIEW") }}');
        addRoute('BE_PRODUCT_COLLECTION', '{{ routes("BE_PRODUCT_COLLECTION") }}');
        addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    </script>

    <!-- Page Scripts -->    
    <script src="{{ contextPath }}/js/pages/product-form.js?{{ buildNumber }}"></script>
    
    <script>
        $(document).ready(function () {
            ProductForm.init();
        });
    </script>
       
{% endblock %}
