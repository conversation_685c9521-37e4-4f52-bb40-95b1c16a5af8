<!-- Product Card -->
<div class="p-5 md:p-8 bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <!-- Title -->
    <div class="mb-4 xl:mb-8">
        <h1 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            Informazioni generali
        </h1>
        <p class="text-sm text-gray-500 dark:text-neutral-500">
            Definis<PERSON> le caratteristiche base del prodotto
        </p>
    </div>
    <!-- End Title -->
    <!-- Form -->
    <form>
        <!-- Logo -->
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Logo prodotto
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-4">
                    <!-- Logo Upload Group -->
                    <input id="logoImage" name="logoImage" type="file" class="filepond w-[100px] cursor-pointer !mb-0">
                    <!-- End Logo Upload Group -->
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->
        </div>
        <!-- End Logo -->     
        <!-- Product Info -->
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <h2 class="font-semibold text-gray-800 dark:text-neutral-200">
                Info prodotto
            </h2>
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label for="version" class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Versione
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-4">
                    <input id="version" name="version" type="text" value="1.0" disabled class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                    <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">
                        La versione di prodotto è automatica e si aggiorna ad ogni modifica
                    </p>
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->

            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label for="code" class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Codice
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-4">
                    <input id="code" name="code" type="text" placeholder="Codice prodotto" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                    <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">
                        Il codice di prodotto è un breve identificativo univoco
                    </p>
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->

            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label for="name" class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Nome
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-4">
                    <input id="name" name="name" type="text" placeholder="Nome prodotto" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600">
                    <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">
                        Il nome del prodotto visibile a catalogo
                    </p>
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->

            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label for="description" class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Descrizione
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-4">
                    <textarea id="description" name="description" class="py-2 px-3 sm:py-3 sm:px-4 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" rows="3" placeholder="Descrizione prodotto"></textarea>
                    <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">
                        Breve descrizione del prodotto
                    </p>
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->                                   

        </div>
        <!-- End Product Info -->

        <!-- Categories -->
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <div class="inline-flex items-center gap-x-2">
                <h2 class="font-semibold text-gray-800 dark:text-neutral-200">
                    Categorie
                </h2>                
            </div>
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label for="category" class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Categoria
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-4">            
                    <!-- Select -->
                    <select id="category" name="category" data-hs-select='{
                            "placeholder": "Seleziona categoria...",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                            "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                            "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                            }' class="hidden">                
                        <option value="vehicles">Veicoli</option>                
                    </select>
                    <!-- End Select -->            
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label for="subcategory" class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Sottocategoria
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-4">            
                    <!-- Select -->
                    <select id="subcategory" name="subcategory" data-hs-select='{
                            "placeholder": "Seleziona sottocategoria...",
                            "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                            "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                            "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                            }' class="hidden">                
                        <option value="motorbike">Moto</option>                
                    </select>
                    <!-- End Select -->            
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->
        </div>
        <!-- End Categories -->      

        <!-- Brands -->
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <div class="inline-flex items-center gap-x-2">
                <h2 class="font-semibold text-gray-800 dark:text-neutral-200">
                    Marchi target
                </h2>  
                <!-- Tooltip -->
                <div class="hs-tooltip inline-block">
                    <svg class="hs-tooltip-toggle shrink-0 ms-1 size-3 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
                    </svg>
                    <div class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity inline-block absolute invisible z-60 p-4 w-96 bg-white rounded-xl shadow-xl dark:bg-neutral-900 dark:text-neutral-400" role="tooltip">
                        <p class="font-medium text-gray-800 dark:text-neutral-200">
                            Marchi target del prodotto:
                        </p>
                        <p class="mt-1 text-sm font-normal text-gray-500 dark:text-neutral-400">
                            Indica per quale/i marchio/i questo prodotto assicurativo è specificamente creato:
                        </p>
                        <ul class="mt-1 ps-3.5 list-disc list-outside text-sm font-normal text-gray-500 dark:text-neutral-400">
                            <li>
                                Definisce l'identità commerciale del prodotto assicurativo
                            </li>
                            <li>
                                Influenza branding, pricing e condizioni specifiche del marchio
                            </li>
                            <li>
                                Puoi associare il prodotto a più marchi se condividono le stesse caratteristiche
                            </li>                            
                        </ul>
                    </div>
                </div>
                <!-- End Tooltip -->
            </div>
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label for="brands" class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Marchi
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-4">                                
                    <!-- Multi Select -->

                    <select id="brands" name="brands" multiple="" data-hs-select='{
                            "placeholder": "Seleziona marchi...",
                            "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "mode": "tags",
                            "wrapperClasses": "relative ps-0.5 pe-9 min-h-[38px] flex items-center flex-wrap text-nowrap w-full border border-gray-200 rounded-lg text-start text-sm focus:border-stone-800 focus:ring-stone-800 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300",
                            "tagsItemTemplate": "<div class=\"flex flex-nowrap items-center relative z-10 bg-white border border-gray-200 rounded-full py-1 px-2 m-0.5 dark:bg-neutral-900 dark:border-neutral-700\"><div class=\"size-4 me-1 flex items-center justify-center\" data-icon></div><div class=\"whitespace-nowrap text-gray-800 dark:text-neutral-200 leading-4 text-sm\" data-title></div><div class=\"inline-flex shrink-0 justify-center items-center size-4 ms-1.5 rounded-full text-gray-800 bg-gray-200 hover:bg-gray-300 focus:outline-hidden focus:ring-2 focus:ring-gray-400 text-xs dark:bg-neutral-700/50 dark:hover:bg-neutral-700 dark:text-neutral-400 cursor-pointer\" data-remove><svg class=\"shrink-0 size-2.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M18 6 6 18\"/><path d=\"m6 6 12 12\"/></svg></div></div>",
                            "tagsInputId": "hs-tags-input",
                            "tagsInputClasses": "py-1 px-2 min-w-16 rounded-lg order-1 border-transparent focus:ring-0 text-sm outline-hidden dark:bg-transparent dark:placeholder-neutral-500 dark:text-neutral-300",
                            "optionTemplate": "<div class=\"flex items-center\"><div class=\"size-5 me-2\" data-icon></div><div class=\"text-sm font-semibold text-gray-800 dark:text-neutral-200\" data-title></div><div class=\"ms-auto\"><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-4 text-gray-800\" xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 16 16\"><path d=\"M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z\"/></svg></span></div></div>",
                            "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                            }' class="hidden">
                        <option value="">Seleziona marchi</option>
                        <option value="1" data-hs-select-option='{
                                "icon": "<img class=\"inline-block rounded-full\" src=\"{{ contextPath }}/img/brands/harley.svg\" />"
                                }'>Harley Davidson</option>
                        <option value="2" data-hs-select-option='{
                                "icon": "<img class=\"inline-block rounded-full\" src=\"{{ contextPath }}/img/brands/ducati.svg\" />"
                                }'>Ducati</option>
                        <option value="3" data-hs-select-option='{
                                "icon": "<img class=\"inline-block rounded-full\" src=\"{{ contextPath }}/img/brands/triumph.svg\" />"
                                }'>Triumph</option>
                        <option value="4" data-hs-select-option='{
                                "icon": "<img class=\"inline-block rounded-full\" src=\"{{ contextPath }}/img/brands/bmw.svg\" />"
                                }'>BMW</option>
                        <option value="5" data-hs-select-option='{
                                "icon": "<img class=\"inline-block rounded-full\" src=\"{{ contextPath }}/img/brands/motoprotection.svg\" />"
                                }'>Motoprotection (altri marchi)</option>
                    </select>


                    <!-- End Multi Select -->            
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->            
        </div>
        <!-- End Brands -->

    </form>
    <!-- End Form -->
</div>
<!-- End Product Card -->