<!-- Product Card -->
<div class="p-5 md:p-8 bg-white border border-gray-200 shadow-2xs rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <!-- Title -->
    <div class="mb-4 xl:mb-8">
        <h1 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            Configurazione Garanzie
        </h1>
        <p class="text-sm text-gray-500 dark:text-neutral-500">
            Definisci le garanzie disponibili e i loro criteri di applicazione
        </p>
    </div>
    <!-- End Title -->
    <!-- Form -->
    <form>
        <!-- Logo -->
        <div class="py-6 sm:py-8 space-y-5 border-t border-gray-200 first:border-t-0 dark:border-neutral-700">
            <!-- Grid -->
            <div class="grid sm:grid-cols-12 gap-y-1.5 sm:gap-y-0 sm:gap-x-5">
                <div class="sm:col-span-4 xl:col-span-3 2xl:col-span-2">
                    <label class="sm:mt-2.5 inline-block text-sm text-gray-500 dark:text-neutral-500">
                        Garanzie
                    </label>
                </div>
                <!-- End Col -->
                <div class="sm:col-span-8 xl:col-span-8">



                    <!-- Empty State -->
                    <div class="p-5 flex flex-col justify-center items-center text-center bg-gray-50 border border-gray-200 text-sm text-gray-600 rounded-lg p-4 dark:bg-white/10 dark:border-white/10 dark:text-neutral-400">
                        <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"></rect>
                            <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"></rect>
                            <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"></rect>
                            <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"></rect>
                            <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"></rect>
                            <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"></rect>
                            <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"></rect>
                            <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"></rect>
                            <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"></rect>
                            <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"></rect>
                            <g filter="url(#filter17)">
                                <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"></rect>
                                <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"></rect>
                                <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "></rect>
                                <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"></rect>
                                <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"></rect>
                            </g>
                            <defs>
                                <filter id="filter17" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                    <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix>
                                    <feOffset dy="6"></feOffset>
                                    <feGaussianBlur stdDeviation="6"></feGaussianBlur>
                                    <feComposite in2="hardAlpha" operator="out"></feComposite>
                                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"></feColorMatrix>
                                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"></feBlend>
                                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"></feBlend>
                                </filter>
                            </defs>
                        </svg>

                        <div class="max-w-sm mx-auto">
                            <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                Carica la tua prima garanzia
                            </p>
                            <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                Cerca fra garanzie già caricate o inserisci una nuova garanzia ora
                            </p>
                        </div>


                        <button type="button" class="py-2 px-3 inline-flex justify-center items-center gap-x-2 text-start bg-gray-800 border border-gray-800 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-950 focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:text-neutral-800 dark:hover:bg-neutral-200 dark:focus:bg-neutral-200" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-pro-eakcmpd" data-hs-overlay="#hs-pro-eakcmpd">
                            <svg class="block shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"/><path d="M12 5v14"/></svg>
                            Inserisci garanzia
                        </button>
                    </div>
                    <!-- End Empty State -->


                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->
        </div>
        <!-- End Logo -->     

    </form>
    <!-- End Form -->
</div>
<!-- End Product Card -->


<!-- Tasks Offcanvas -->
<div id="hs-pro-eakcmpd" class="hs-overlay [--body-scroll:true] [--is-layout-affect:true] [--auto-close:2xl] [--has-dynamic-z-index:true] hs-overlay-open:translate-x-0
     translate-x-full transition-all duration-300 transform
     size-full sm:w-120
     hidden
     fixed top-0 end-0 z-89 
     flex-1 flex flex-col
     bg-white border-s border-gray-200
     2xl:translate-x-full
     dark:bg-neutral-800 dark:border-neutral-700" tabindex="-1" aria-labelledby="hs-pro-eakcmpd-label">
    <!-- Header -->
    <div class="py-3 px-4 flex justify-between items-center border-b border-gray-200 dark:border-neutral-700">
        <h3 id="hs-pro-eakcmpd-label" class="font-semibold text-gray-800 dark:text-neutral-200">
            Inserisci garanzia
        </h3>    
    </div>
    <!-- End Header -->

    <!-- Body -->
    <div class="p-5 h-full flex-1 flex flex-col overflow-y-auto overflow-hidden [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">     

        <nav class="relative flex gap-x-1 after:absolute after:bottom-0 after:inset-x-0 after:border-b after:border-gray-200 dark:after:border-neutral-700" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
            <button type="button" class="hs-tab-active:text-stone-800 hs-tab-active:after:bg-stone-800 hs-tab-active:dark:text-neutral-200 hs-tab-active:dark:after:bg-neutral-400 px-2.5 py-1.5 mb-2 relative inline-flex items-center gap-x-2 hover:bg-stone-200/70 text-stone-500 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-stone-200/50 after:absolute after:-bottom-2 after:inset-x-0 after:z-1 after:h-0.5 after:pointer-events-none dark:hover:bg-neutral-700/50 dark:focus:bg-neutral-700/50 active" id="tabs-with-underline-item-1" aria-selected="true" data-hs-tab="#tabs-with-underline-1" aria-controls="tabs-with-underline-1" role="tab">
                Nuova
            </button>
            <button type="button" class="hs-tab-active:text-stone-800 hs-tab-active:after:bg-stone-800 hs-tab-active:dark:text-neutral-200 hs-tab-active:dark:after:bg-neutral-400 px-2.5 py-1.5 mb-2 relative inline-flex items-center gap-x-2 hover:bg-stone-200/70 text-stone-500 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-stone-200/50 after:absolute after:-bottom-2 after:inset-x-0 after:z-1 after:h-0.5 after:pointer-events-none dark:hover:bg-neutral-700/50 dark:focus:bg-neutral-700/50" id="tabs-with-underline-item-2" aria-selected="false" data-hs-tab="#tabs-with-underline-2" aria-controls="tabs-with-underline-2" role="tab">
                Cerca
            </button>
        </nav>
        <div class="mt-3">
            <div id="tabs-with-underline-1" role="tabpanel" aria-labelledby="tabs-with-underline-item-1">

                <!-- Form Garanzie -->
                <form class="space-y-5 mt-5">
                    <!-- Codice Garanzia -->
                    <div>
                        <label for="codiceGaranzia" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                            Codice garanzia <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="codiceGaranzia" name="codiceGaranzia" required class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-stone-800 focus:ring-stone-800 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Inserisci codice">
                    </div>

                    <!-- Titolo Garanzia -->
                    <div>
                        <label for="titoloGaranzia" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                            Titolo garanzia <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="titoloGaranzia" name="titoloGaranzia" required class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-stone-800 focus:ring-stone-800 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Inserisci titolo">
                    </div>

                    <!-- Tipo Garanzia -->
                    <div>
                        <label for="tipoGaranzia" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                            Tipo garanzia <span class="text-red-500">*</span>
                        </label>
                        <select id="tipoGaranzia" name="tipoGaranzia" required data-hs-select='{
                                "placeholder": "Seleziona tipo garanzia...",
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 sm:py-2 ps-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start sm:text-sm focus:outline-hidden focus:ring-2 focus:ring-stone-800 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:focus:outline-hidden dark:focus:ring-neutral-600",
                                "dropdownClasses": "mt-2 max-h-72 p-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div><div class=\"hs-selected:font-semibold text-sm text-gray-800 dark:text-neutral-200\" data-title></div></div><div class=\"ms-auto\"><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-4 text-blue-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 16 16\"><path d=\"M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z\"/></svg></span></div></div>",
                                "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                                }' class="hidden">
                            <option value="">Scegli</option>
                            <option value="responsabilita_civile" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10\"/></svg>"}'>
                                Responsabilità civile
                            </option>
                            <option value="incendio_furto" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z\"/></svg>"}'>
                                Incendio e furto
                            </option>
                            <option value="assistenza_stradale" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M10 10H6\"/><path d=\"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2\"/><path d=\"M19 18h2a1 1 0 0 0 1-1v-3.28a1 1 0 0 0-.684-.948l-1.923-.641a1 1 0 0 1-.578-.502l-1.539-3.076A1 1 0 0 0 16.382 8H14\"/><path d=\"M8 8v4\"/><path d=\"M9 18h6\"/><circle cx=\"17\" cy=\"18\" r=\"2\"/><circle cx=\"7\" cy=\"18\" r=\"2\"/></svg>"}'>
                                Assistenza stradale
                            </option>
                            <option value="tutela_legale" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z\"/><path d=\"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z\"/><path d=\"M7 21h10\"/><path d=\"M12 3v18\"/><path d=\"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2\"/></svg>"}'>
                                Tutela legale
                            </option>
                            <option value="infortuni_conducente" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 11v4\"/><path d=\"M14 13h-4\"/><path d=\"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2\"/><path d=\"M18 6v14\"/><path d=\"M6 6v14\"/><rect width=\"20\" height=\"14\" x=\"2\" y=\"6\" rx=\"2\"/></svg>"}'>
                                Infortuni del conducente
                            </option>
                        </select>
                    </div>

                    <!-- Compagnia -->
                    <div>
                        <label for="compagnia" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                            Compagnia <span class="text-red-500">*</span>
                        </label>
                        <select id="compagnia" name="compagnia" data-hs-select='{
                                "placeholder": "Seleziona compagnia...",
                                "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-1.5 sm:py-2 ps-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start sm:text-sm focus:outline-hidden focus:ring-2 focus:ring-stone-800 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:focus:outline-hidden dark:focus:ring-neutral-600",
                                "dropdownClasses": "mt-2 max-h-72 p-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div><div class=\"hs-selected:font-semibold text-sm text-gray-800 dark:text-neutral-200\" data-title></div></div><div class=\"ms-auto\"><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-4 text-blue-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 16 16\"><path d=\"M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z\"/></svg></span></div></div>",
                                "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                                }' class="hidden">
                            <option value="">Scegli</option>
                            <option value="italiana_assicurazioni" data-hs-select-option='{
                                    "icon": "<img class=\"inline-block shrink-0 size-4 rounded-full\" src=\"{{ contextPath }}/img/companies/italiana.jpg\" />"}'>
                                Italiana Assicurazioni
                            </option>
                            <option value="ima" data-hs-select-option='{
                                    "icon": "<img class=\"inline-block shrink-0 size-4 rounded-full\" src=\"{{ contextPath }}/img/companies/ima.png\" />"}'>
                                IMA
                            </option>
                        </select>
                    </div>

                    <!-- Descrizione -->
                    <div>
                        <label for="descrizione" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                            Descrizione <span class="text-red-500">*</span>
                        </label>
                        <textarea id="descrizione" name="descrizione" rows="4" class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-stone-800 focus:ring-stone-800 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Descrivi la garanzia..."></textarea>
                    </div>

                    <!-- Criteri -->
                    <div>
                        <label for="criteri" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                            Criteri <span class="text-red-500">*</span>
                        </label>
                        <select id="criteri" name="criteri" multiple="" data-hs-select='{
                                "placeholder": "Seleziona criteri...",
                                "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "mode": "tags",
                                "wrapperClasses": "relative ps-0.5 pe-9 min-h-[38px] flex items-center flex-wrap text-nowrap w-full border border-gray-200 rounded-lg text-start text-sm focus:border-stone-800 focus:ring-stone-800 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300",
                                "tagsItemTemplate": "<div class=\"flex flex-nowrap items-center relative z-10 bg-white border border-gray-200 rounded-full py-1 px-2 m-0.5 dark:bg-neutral-900 dark:border-neutral-700\"><div class=\"size-4 me-1 flex items-center justify-center\" data-icon></div><div class=\"whitespace-nowrap text-gray-800 dark:text-neutral-200 leading-4 text-sm\" data-title></div><div class=\"inline-flex shrink-0 justify-center items-center size-4 ms-1.5 rounded-full text-gray-800 bg-gray-200 hover:bg-gray-300 focus:outline-hidden focus:ring-2 focus:ring-gray-400 text-xs dark:bg-neutral-700/50 dark:hover:bg-neutral-700 dark:text-neutral-400 cursor-pointer\" data-remove><svg class=\"shrink-0 size-2.5\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M18 6 6 18\"/><path d=\"m6 6 12 12\"/></svg></div></div>",
                                "tagsInputId": "hs-tags-input-criteri",
                                "tagsInputClasses": "py-1 px-2 min-w-16 rounded-lg order-1 border-transparent focus:ring-0 text-sm outline-hidden dark:bg-transparent dark:placeholder-neutral-500 dark:text-neutral-300",
                                "optionTemplate": "<div class=\"flex items-center\"><div class=\"me-2\" data-icon></div><div><div class=\"hs-selected:font-semibold text-sm text-gray-800 dark:text-neutral-200\" data-title></div></div><div class=\"ms-auto\"><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-4 text-blue-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 16 16\"><path d=\"M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z\"/></svg></span></div></div>",
                                "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                                }' class="hidden">
                            <option value="">Seleziona criteri di ricerca</option>
                            <option value="provincia" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"12\" cy=\"10\" r=\"3\"/><path d=\"m12 21.7-7.3-7.3a7.05 7.05 0 0 1 0-10C6.4 2.7 9.1 1.5 12 1.5s5.6 1.2 7.3 2.9a7.05 7.05 0 0 1 0 10L12 21.7z\"/></svg>"}'>
                                Provincia di residenza
                            </option>
                            <option value="situazione_assicurativa" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10\"/><path d=\"m9 12 2 2 4-4\"/></svg>"}'>
                                Situazione assicurativa attuale
                            </option>
                            <option value="num_sinistri" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\"/></svg>"}'>
                                Numero di sinistri negli ultimi 5 anni
                            </option>
                            <option value="cu" data-hs-select-option='{
                                    "icon": "<svg class=\"shrink-0 size-4 text-gray-600\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"/><polyline points=\"14,2 14,8 20,8\"/><line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"/><line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"/></svg>"}'>
                                Certificato di Unanime (CU)
                            </option>
                        </select>
                    </div>

                    <!-- Pulsanti -->
                    <div class="flex gap-x-3">
                        <button type="submit" class="py-2 px-3 inline-flex justify-center items-center gap-x-2 text-start bg-gray-800 border border-gray-800 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-950 focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:text-neutral-800 dark:hover:bg-neutral-200 dark:focus:bg-neutral-200">
                            Salva garanzia
                        </button>
                        <button type="button" data-hs-overlay="#hs-pro-eakcmpd" class="py-2 px-3 inline-flex justify-center items-center gap-x-2 text-start bg-white border border-gray-200 text-gray-800 text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                            Annulla
                        </button>
                    </div>
                </form>
                <!-- End Form Garanzie -->

            </div>
            <div id="tabs-with-underline-2" class="hidden" role="tabpanel" aria-labelledby="tabs-with-underline-item-2">

                <!-- Form Cerca Garanzie-->
                <form class="space-y-5 mt-5">
                    <!-- Cerca -->
                    <div>
                        <label for="searchWarranty" class="block text-sm font-medium mb-2 text-gray-700 dark:text-neutral-200">
                            Cerca garanzia
                        </label>
                        <input type="text" id="searchWarranty" name="searchWarranty" required class="py-1.5 sm:py-2 px-3 block w-full border-gray-200 rounded-lg sm:text-sm placeholder:text-gray-400 focus:border-stone-800 focus:ring-stone-800 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Cerca garanzia">
                    </div>

                    <div class="p-5 flex flex-col bg-gray-50 border border-gray-200 text-sm text-gray-600 rounded-lg p-4 dark:bg-white/10 dark:border-white/10 dark:text-neutral-400">
                        <!-- List -->
                        <dl class="grid grid-cols-2 gap-x-2">
                            <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">
                                Codice:
                            </dt>
                            <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">
                                GAR-2024-001
                            </dd>
                            <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">
                                Titolo:
                            </dt>
                            <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">
                                Responsabilità Civile Base
                            </dd>
                            <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">
                                Tipo:
                            </dt>
                            <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">
                                Responsabilità civile
                            </dd>
                            <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">
                                Compagnia:
                            </dt>
                            <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">
                                Italiana Assicurazioni
                            </dd>                          
                            <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500">
                                Criteri applicabili:
                            </dt>
                            <dd class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">
                                Provincia di residenza, N. sinistri
                            </dd>
                            <dt class="py-1 text-sm text-gray-500 dark:text-neutral-500 col-span-2 border-t border-gray-200 dark:border-neutral-800 pt-2 mt-1">
                                Descrizione:
                            </dt>
                            <dd class="py-1 text-sm text-gray-600 dark:text-neutral-400 col-span-2 leading-relaxed">
                                Polizza assicurativa completa per motocicli che offre copertura RCA con massimali elevati, tutela legale inclusa e assistenza stradale 24/7. Ideale per motociclisti con esperienza che guidano moto di grossa cilindrata su tutto il territorio nazionale.
                            </dd>
                        </dl>
                        <!-- End List -->
                        <div class="block text-end">
                            <button type="button" class="py-2 px-3 mt-2 inline-flex justify-center items-center gap-x-2 text-start bg-gray-800 border border-gray-800 text-white text-sm font-medium rounded-lg shadow-2xs align-middle hover:bg-gray-950 focus:outline-hidden focus:bg-gray-900 dark:bg-white dark:text-neutral-800 dark:hover:bg-neutral-200 dark:focus:bg-neutral-200">                                
                                Aggiungi
                            </button>
                        </div>
                    </div>

                </form>
                <!-- Form Cerca Garanzie -->

            </div>  
        </div>

    </div>
    <!-- End Footer -->
</div>
<!-- End Tasks Offcanvas -->