package controller;

import core.Core;
import core.Pages;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class ProductController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductController.class.getName());

    public static TemplateViewRoute be_product_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        return Core.render(Pages.BE_PRODUCT_COLLECTION, attributes, request);
    };
    
    public static TemplateViewRoute be_product_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        return Core.render(Pages.BE_PRODUCT_EDIT, attributes, request);
    };

}
