package core;

/**
 *
 * <AUTHOR>
 */
public class Pages {

    // errors
    public static final String ERROR_404                    = "fe/404.html";

    // login
    public static final String BE_LOGIN                     = "be/login.html";
    
    // forgot
    public static final String BE_FORGOT                    = "be/forgot.html";
    
    // dashboard
    public static final String BE_DASHBOARD                 = "be/dashboard.html";
    
    // catalog (products)
    public static final String BE_CATALOG_COLLECTION        = "be/catalog-collection.html";
    
    // estimates / contracts
    public static final String BE_ESTIMATE_COLLECTION      = "be/estimate-collection.html";
    public static final String BE_CONTRACT_COLLECTION      = "be/contract-collection.html";    
    public static final String BE_ESTIMATE_VIEW            = "be/estimate-view.html";    
    
    // manteinance / tables
    public static final String BE_TABLE_COLLECTION          = "be/table-collection.html";

    // products (management)
    public static final String BE_PRODUCT_COLLECTION      = "be/product-collection.html";
    public static final String BE_PRODUCT_EDIT            = "be/product-edit.html";
    
    // ========================
    // old
    
    // settings
    public static final String BE_SETTINGS_COMPANY          = "be/settings-company.html";

    public static final String BE_SETTINGS_SMTP_COLLECTION  = "be/settings-smtp-collection.html";
    public static final String BE_SETTINGS_SMTP             = "be/settings-smtp.html";

    public static final String BE_SETTINGS_USER_COLLECTION  = "be/settings-user-collection.html";
    public static final String BE_SETTINGS_USER             = "be/settings-user.html";

    // user
    public static final String BE_USER_COLLECTION           = "be/user-collection.html";
    public static final String BE_USER                      = "be/user.html";
    public static final String BE_USER_FORM                 = "be/include/snippets/pojo/user-form.html";
    public static final String BE_USER_PERMISSIONS_MANAGER  = "be/user-permissions-manager.html";

    // contact
    public static final String BE_CONTACT_COLLECTION       = "be/contact-collection.html";
    public static final String BE_CONTACT                  = "be/contact.html";
    public static final String BE_CONTACT_FORM             = "be/include/snippets/pojo/contact-form.html";

    // country
    public static final String BE_COUNTRY_COLLECTION       = "be/country-collection.html";
    public static final String BE_COUNTRY                  = "be/country.html";
    public static final String BE_COUNTRY_FORM             = "be/include/snippets/pojo/country-form.html";

    // province
    public static final String BE_PROVINCE_COLLECTION      = "be/province-collection.html";
    public static final String BE_PROVINCE                 = "be/province.html";
    public static final String BE_PROVINCE_FORM            = "be/include/snippets/pojo/province-form.html";

    // city
    public static final String BE_CITY_COLLECTION          = "be/city-collection.html";
    public static final String BE_CITY                     = "be/city.html";
    public static final String BE_CITY_FORM                = "be/include/snippets/pojo/city-form.html";

    // brand
    public static final String BE_BRAND_COLLECTION         = "be/brand-collection.html";
    public static final String BE_BRAND                    = "be/brand.html";
    public static final String BE_BRAND_FORM               = "be/include/snippets/pojo/brand-form.html";

    // message
    public static final String BE_MESSAGE_COLLECTION       = "be/message-collection.html";
    public static final String BE_MESSAGE                  = "be/message.html";
    public static final String BE_MESSAGE_FORM             = "be/include/snippets/pojo/message-form.html";

    // accesslog
    public static final String BE_ACCESSLOG_COLLECTION     = "be/accesslog-collection.html";
    public static final String BE_ACCESSLOG_FORM           = "be/include/snippets/pojo/accesslog-form.html";

    // model
    public static final String BE_MODEL_COLLECTION         = "be/model-collection.html";
    public static final String BE_MODEL                    = "be/model.html";
    public static final String BE_MODEL_FORM               = "be/include/snippets/pojo/model-form.html";

    // modelsetup
    public static final String BE_MODELSETUP_COLLECTION    = "be/modelsetup-collection.html";
    public static final String BE_MODELSETUP               = "be/modelsetup.html";
    public static final String BE_MODELSETUP_FORM          = "be/include/snippets/pojo/modelsetup-form.html";

    // dealer
    public static final String BE_DEALER_COLLECTION         = "be/dealer-collection.html";
    public static final String BE_DEALER_VIEW               = "be/dealer-view.html";
    public static final String BE_DEALER_EDIT               = "be/dealer-edit.html";
    public static final String BE_DEALER_FORM               = "be/include/snippets/pojo/dealer-form.html";

    // insurance company
    public static final String BE_INSURANCECOMPANY_COLLECTION = "be/insurancecompany-collection.html";
    public static final String BE_INSURANCECOMPANY         = "be/insurancecompany.html";

    // warranty
    public static final String BE_WARRANTY_COLLECTION      = "be/warranty-collection.html";
    public static final String BE_WARRANTY                 = "be/warranty.html";
    public static final String BE_WARRANTY_FORM            = "be/include/snippets/pojo/warranty-form.html";
    public static final String BE_WARRANTY_CRITERIA_TEST   = "be/warranty-criteria-test.html";

    // warranty details
    public static final String BE_WARRANTYDETAILS_COLLECTION = "be/warrantydetails-collection.html";
    public static final String BE_WARRANTYDETAILS          = "be/warrantydetails.html";

    // channel
    public static final String BE_CHANNEL_COLLECTION       = "be/channel-collection.html";
    public static final String BE_CHANNEL                  = "be/channel.html";

    // warranty type
    public static final String BE_WARRANTYTYPE_COLLECTION  = "be/warrantytype-collection.html";
    public static final String BE_WARRANTYTYPE             = "be/warrantytype.html";
    public static final String BE_WARRANTYTYPE_FORM        = "be/include/snippets/pojo/warrantytype-form.html";

    // insurance provenance type
    public static final String BE_INSURANCEPROVENANCETYPE_COLLECTION = "be/insuranceprovenancetype-collection.html";
    public static final String BE_INSURANCEPROVENANCETYPE  = "be/insuranceprovenancetype.html";

    // field translation
    public static final String BE_FIELDTRANSLATION_COLLECTION = "be/fieldtranslation-collection.html";
    public static final String BE_FIELDTRANSLATION = "be/fieldtranslation.html";

    // mailtemplate
    public static final String BE_MAILTEMPLATE_COLLECTION   = "be/mailtemplate-collection.html";
    public static final String BE_MAILTEMPLATE              = "be/mailtemplate.html";

    // import
    public static final String BE_IMPORT                    = "be/import.html";

}
