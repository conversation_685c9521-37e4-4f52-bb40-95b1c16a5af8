const BrandForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentFilePond();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitBrand();
        _componentInputFormatting();
    };

    // Validation config using TiganiLibs
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Custom validation rules for brand
        $.validator.addMethod('brandCode', function (value) {
            return /^[A-Z0-9]+$/.test(value);
        }, 'Il codice può contenere solo lettere maiuscole e numeri.');

        // Define custom rules for this form
        const customRules = {
            code: {
                required: true,
                brandCode: true,
                maxlength: 10
            },
            name: {
                required: true,
                maxlength: 100
            }
        };

        // Custom options for this form
        const customOptions = {
            ignore: 'input[type=hidden], .select2-search__field'
        };

        // Initialize validation using TiganiLibs factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', customRules, customOptions);

    };

    // Maxlength using TiganiLibs
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Initialize maxlength using TiganiLibs
        TiganiLibs.ComponentUtils.initMaxlength('.maxlength');
    };

    // FilePond using centralized factory
    const _componentFilePond = function () {
        if (!window.FilePond) {
            console.warn('Warning - FilePond is not loaded.');
            return;
        }

        // Initialize FilePond using TiganiLibs factory
        const pond = TiganiLibs.UIComponentFactory.initFilePond('input[type="file"]', {
            stylePanelLayout: 'compact',
            stylePanelAspectRatio: '1:1',
            imageCropAspectRatio: '1:1',
            imageResizeTargetWidth: 200,
            imageResizeTargetHeight: 200,
            maxFileSize: '2MB'
        });

        // Load initial image if present
        var imageId = pageVariables.get("imageId");
        if (typeof imageId !== "undefined" && imageId) {
            var image = appRoutes.get("BE_IMAGE") + "?oid=" + imageId;
            pond.addFile(image);
        }
    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-brand-btn',
            permissionCheck: 'BRAND_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-brandid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questo marchio? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(brandId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('brandIds', brandId);
                formData.append('operation', 'delete');

                $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_BRAND_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.brandsDataTable && window.brandsDataTable.dataTable) {
                            window.brandsDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Marchio eliminato correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Marchio eliminato correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during brand deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('BRAND_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#brand-edit input, #brand-edit textarea, #brand-edit select').prop('readonly', true);
            $('#brand-edit select').prop('disabled', true);
            $('#brand-edit-offcanvas input, #brand-edit-offcanvas textarea, #brand-edit-offcanvas select').prop('readonly', true);
            $('#brand-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#brand-edit, #brand-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new brand forms
        const isNewBrand = !$('#brand-edit input[name="id"]').val() && !$('#brand-edit-offcanvas input[name="id"]').val();
        if (isNewBrand && !hasPermission('BRAND_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#brand-edit input, #brand-edit textarea, #brand-edit select').prop('disabled', true);
            $('#brand-edit-offcanvas input, #brand-edit-offcanvas textarea, #brand-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#brand-edit, #brand-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitBrand = function () {
        var idForm = "brand-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewBrand = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewBrand) {
                    if (!hasPermission('BRAND_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare marchi.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('BRAND_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare marchi.', 'error');
                        return;
                    }
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.brandsDataTable && window.brandsDataTable.dataTable) {
                                window.brandsDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Marchio salvato correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Marchio salvato, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during brand save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Code field - uppercase transformation
        $('input[name="codice"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Description field - trim whitespace
        $('input[name="descrizione"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    BrandForm.init();
});
